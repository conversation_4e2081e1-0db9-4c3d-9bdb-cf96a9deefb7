import { useState, useEffect, useCallback, useRef } from "react";
import { useSDK } from "./useSDK";

interface UseNotificationsReturn {
  unreadCount: number;
  refreshUnreadCount: () => Promise<void>;
  loading: boolean;
}

export const useNotifications = (): UseNotificationsReturn => {
  const { sdk } = useSDK();
  const [unreadCount, setUnreadCount] = useState(3); // Mock data for now
  const [loading, setLoading] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isLoadingRef = useRef(false);

  const refreshUnreadCount = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setLoading(true);

      // For now, just use mock data instead of API call
      // const response = await sdk.request({
      //   endpoint: "/v2/api/ebadollar/custom/member/notifications/unread-count",
      //   method: "GET",
      // });

      // if (!response.error) {
      //   setUnreadCount(Number(response.data?.unread_count || 0));
      // }

      // Mock: randomly change the count for demo purposes
      setUnreadCount(Math.floor(Math.random() * 5));
    } catch (error) {
      console.error("Error loading unread notification count:", error);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Initial load
    refreshUnreadCount();

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up new interval - refresh count every 60 seconds (increased from 30)
    intervalRef.current = setInterval(refreshUnreadCount, 60000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      isLoadingRef.current = false;
    };
  }, []); // Remove refreshUnreadCount from dependencies to prevent recreation

  return {
    unreadCount,
    refreshUnreadCount,
    loading,
  };
};
