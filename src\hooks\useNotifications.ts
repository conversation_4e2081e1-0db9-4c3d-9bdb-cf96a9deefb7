import { useState, useEffect, useCallback } from "react";
import { useSDK } from "./useSDK";

interface UseNotificationsReturn {
  unreadCount: number;
  refreshUnreadCount: () => Promise<void>;
  loading: boolean;
}

export const useNotifications = (): UseNotificationsReturn => {
  const { sdk } = useSDK();
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const refreshUnreadCount = useCallback(async () => {
    try {
      setLoading(true);
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/notifications/unread-count",
        method: "GET",
      });

      if (!response.error) {
        setUnreadCount(Number(response.data?.unread_count || 0));
      }
    } catch (error) {
      console.error("Error loading unread notification count:", error);
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  useEffect(() => {
    refreshUnreadCount();
    
    // Refresh count every 30 seconds
    const interval = setInterval(refreshUnreadCount, 30000);
    
    return () => clearInterval(interval);
  }, [refreshUnreadCount]);

  return {
    unreadCount,
    refreshUnreadCount,
    loading,
  };
};
