import { useState, useCallback } from "react";

interface UseNotificationsReturn {
  unreadCount: number;
  refreshUnreadCount: () => Promise<void>;
  loading: boolean;
}

export const useNotifications = (): UseNotificationsReturn => {
  // Mock data - static unread count for now
  const [unreadCount, setUnreadCount] = useState(3);
  const [loading, setLoading] = useState(false);

  const refreshUnreadCount = useCallback(async () => {
    try {
      setLoading(true);

      // For now, just use mock data - no API calls
      // When you're ready to connect to real API, uncomment below:
      // const response = await sdk.request({
      //   endpoint: "/v2/api/ebadollar/custom/member/notifications/unread-count",
      //   method: "GET",
      // });
      // if (!response.error) {
      //   setUnreadCount(Number(response.data?.unread_count || 0));
      // }

      // Mock: simulate updating the count
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error loading unread notification count:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    unreadCount,
    refreshUnreadCount,
    loading,
  };
};
