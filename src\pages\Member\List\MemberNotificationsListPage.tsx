import React, { useState, useEffect, useCallback } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";
import { useNotifications } from "../../../hooks/useNotifications";
import { InteractiveButton } from "../../../components/InteractiveButton";
import {
  BellIcon,
  CheckIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";

// Interface definitions
interface Notification {
  id: number;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  is_read: boolean;
  created_at: string;
  updated_at: string;
  action_url?: string;
  action_text?: string;
}

const MemberNotificationsListPage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const { refreshUnreadCount } = useNotifications();

  // State management
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<"all" | "unread" | "read">(
    "all"
  );
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>(
    []
  );
  const [actionLoading, setActionLoading] = useState(false);

  // Load notifications on component mount and filter change
  useEffect(() => {
    loadNotifications();
  }, [activeFilter]);

  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/notifications",
        method: "GET",
        params: { filter: activeFilter },
      });

      console.log("Notifications API Response:", response);

      if (!response.error) {
        // Process and validate the data
        const notificationData = Array.isArray(response.data?.notifications)
          ? response.data.notifications.map((notif: any) => ({
              id: Number(notif.id || 0),
              title: String(notif.title || ""),
              message: String(notif.message || ""),
              type: String(notif.type || "info") as
                | "info"
                | "success"
                | "warning"
                | "error",
              is_read: Boolean(notif.is_read),
              created_at: String(notif.created_at || ""),
              updated_at: String(notif.updated_at || ""),
              action_url: notif.action_url || undefined,
              action_text: notif.action_text || undefined,
            }))
          : [];

        setNotifications(notificationData);
      } else {
        setError(String(response.message || "Failed to load notifications"));
      }
    } catch (error: any) {
      console.error("Error loading notifications:", error);
      setError(String(error?.message || "An unexpected error occurred"));
    } finally {
      setLoading(false);
    }
  }, [sdk, activeFilter]);

  const markAsRead = useCallback(
    async (notificationIds: number[]) => {
      try {
        setActionLoading(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/notifications/mark-read",
          method: "POST",
          body: { notification_ids: notificationIds },
        });

        if (!response.error) {
          // Update local state
          setNotifications((prev) =>
            prev.map((notif) =>
              notificationIds.includes(notif.id)
                ? { ...notif, is_read: true }
                : notif
            )
          );
          setSelectedNotifications([]);
          success("Notifications marked as read");
          // Refresh the global unread count
          refreshUnreadCount();
        } else {
          showError(
            String(response.message || "Failed to mark notifications as read")
          );
        }
      } catch (error: any) {
        console.error("Error marking notifications as read:", error);
        showError(
          String(error?.message || "Failed to mark notifications as read")
        );
      } finally {
        setActionLoading(false);
      }
    },
    [sdk, success, showError, refreshUnreadCount]
  );

  const deleteNotifications = useCallback(
    async (notificationIds: number[]) => {
      try {
        setActionLoading(true);

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/notifications/delete",
          method: "DELETE",
          body: { notification_ids: notificationIds },
        });

        if (!response.error) {
          // Update local state
          setNotifications((prev) =>
            prev.filter((notif) => !notificationIds.includes(notif.id))
          );
          setSelectedNotifications([]);
          success("Notifications deleted");
        } else {
          showError(
            String(response.message || "Failed to delete notifications")
          );
        }
      } catch (error: any) {
        console.error("Error deleting notifications:", error);
        showError(String(error?.message || "Failed to delete notifications"));
      } finally {
        setActionLoading(false);
      }
    },
    [sdk, success, showError]
  );

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} min${diffInMinutes !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case "warning":
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />;
      case "error":
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <InformationCircleIcon className="h-6 w-6 text-blue-500" />;
    }
  };

  const toggleNotificationSelection = (notificationId: number) => {
    setSelectedNotifications((prev) =>
      prev.includes(notificationId)
        ? prev.filter((id) => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const toggleSelectAll = () => {
    const filteredNotifications = getFilteredNotifications();
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map((n) => n.id));
    }
  };

  const getFilteredNotifications = () => {
    switch (activeFilter) {
      case "unread":
        return notifications.filter((n) => !n.is_read);
      case "read":
        return notifications.filter((n) => n.is_read);
      default:
        return notifications;
    }
  };

  const filteredNotifications = getFilteredNotifications();
  const unreadCount = notifications.filter((n) => !n.is_read).length;

  return (
    <MemberWrapper>
      <div className="h-full" style={{ backgroundColor: "#0F2C59" }}>
        <div className="p-6">
          {/* Header */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <BellIcon className="h-8 w-8 text-[#0F2C59] mr-3" />
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Notifications
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Stay updated with your latest activities
                    {unreadCount > 0 && (
                      <span className="ml-2 bg-[#E63946] text-white px-2 py-1 rounded-full text-xs font-medium">
                        {unreadCount} unread
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
              {[
                { key: "all", label: "All", count: notifications.length },
                { key: "unread", label: "Unread", count: unreadCount },
                {
                  key: "read",
                  label: "Read",
                  count: notifications.length - unreadCount,
                },
              ].map((filter) => (
                <button
                  key={filter.key}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeFilter === filter.key
                      ? "bg-[#0F2C59] text-white"
                      : "text-gray-600 hover:bg-gray-200"
                  }`}
                  onClick={() => setActiveFilter(filter.key as any)}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Bulk Actions */}
            {selectedNotifications.length > 0 && (
              <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg mb-4">
                <span className="text-sm text-gray-600">
                  {selectedNotifications.length} notification
                  {selectedNotifications.length !== 1 ? "s" : ""} selected
                </span>
                <div className="flex space-x-2">
                  <InteractiveButton
                    className="px-4 py-2 text-sm bg-[#0F2C59] text-white rounded-md hover:bg-opacity-90"
                    onClick={() => markAsRead(selectedNotifications)}
                    disabled={actionLoading}
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Mark as Read
                  </InteractiveButton>
                  <InteractiveButton
                    className="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                    onClick={() => deleteNotifications(selectedNotifications)}
                    disabled={actionLoading}
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    Delete
                  </InteractiveButton>
                </div>
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="bg-white rounded-lg">
            {loading ? (
              <div className="flex items-center justify-center p-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0F2C59]"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center p-12 text-red-500">
                <div className="text-center">
                  <XCircleIcon className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg mb-2">{error}</p>
                  <InteractiveButton
                    onClick={loadNotifications}
                    className="px-4 py-2 bg-[#0F2C59] text-white rounded hover:bg-opacity-90"
                  >
                    Retry
                  </InteractiveButton>
                </div>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="flex items-center justify-center p-12 text-gray-500">
                <div className="text-center">
                  <BellIcon className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg mb-2">No notifications</p>
                  <p className="text-sm">
                    {activeFilter === "unread"
                      ? "You're all caught up! No unread notifications."
                      : activeFilter === "read"
                        ? "No read notifications yet."
                        : "You don't have any notifications yet."}
                  </p>
                </div>
              </div>
            ) : (
              <>
                {/* Select All Header */}
                <div className="flex items-center p-4 border-b border-gray-200">
                  <input
                    type="checkbox"
                    checked={
                      selectedNotifications.length ===
                      filteredNotifications.length
                    }
                    onChange={toggleSelectAll}
                    className="h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
                  />
                  <span className="ml-3 text-sm text-gray-600">
                    Select all notifications
                  </span>
                </div>

                {/* Notifications */}
                <div className="divide-y divide-gray-200">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start p-4 hover:bg-gray-50 transition-colors ${
                        !notification.is_read ? "bg-blue-50" : ""
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(
                          notification.id
                        )}
                        onChange={() =>
                          toggleNotificationSelection(notification.id)
                        }
                        className="h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded mt-1"
                      />

                      <div className="ml-3 flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="ml-3 flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3
                              className={`text-sm font-medium ${
                                !notification.is_read
                                  ? "text-gray-900"
                                  : "text-gray-700"
                              }`}
                            >
                              {notification.title}
                            </h3>
                            <p
                              className={`mt-1 text-sm ${
                                !notification.is_read
                                  ? "text-gray-800"
                                  : "text-gray-600"
                              }`}
                            >
                              {notification.message}
                            </p>
                            {notification.action_url &&
                              notification.action_text && (
                                <InteractiveButton
                                  className="mt-2 text-sm text-[#0F2C59] hover:text-[#E63946] font-medium"
                                  onClick={() =>
                                    window.open(
                                      notification.action_url,
                                      "_blank"
                                    )
                                  }
                                >
                                  {notification.action_text} →
                                </InteractiveButton>
                              )}
                          </div>

                          <div className="flex items-center ml-4">
                            {!notification.is_read && (
                              <span className="w-2 h-2 bg-[#E63946] rounded-full mr-3"></span>
                            )}
                            <span className="text-xs text-gray-500 whitespace-nowrap">
                              {formatTime(notification.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberNotificationsListPage;
