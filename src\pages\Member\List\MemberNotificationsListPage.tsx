import React, { useState, useEffect, useCallback } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { useToast } from "../../../hooks/useToast";
import { useNotifications } from "../../../hooks/useNotifications";
import { InteractiveButton } from "../../../components/InteractiveButton";
import {
  BellIcon,
  CheckIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";

// Interface definitions
interface Notification {
  id: number;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  is_read: boolean;
  created_at: string;
  updated_at: string;
  action_url?: string;
  action_text?: string;
}

// Mock data for notifications
const MOCK_NOTIFICATIONS: Notification[] = [
  {
    id: 1,
    title: "New Order Received",
    message:
      "You have received a new order for 'Premium Digital Marketing Course'. The buyer is waiting for confirmation.",
    type: "success",
    is_read: false,
    created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    updated_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    action_url: "/member/transactions",
    action_text: "View Order",
  },
  {
    id: 2,
    title: "Payment Processed",
    message:
      "Your payment of eBa$ 150.00 has been successfully processed and added to your account balance.",
    type: "success",
    is_read: false,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
  },
  {
    id: 3,
    title: "Listing Approved",
    message:
      "Your listing 'Web Development Services' has been approved and is now live on the marketplace.",
    type: "info",
    is_read: true,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
    action_url: "/member/listings",
    action_text: "View Listing",
  },
  {
    id: 4,
    title: "Account Verification Required",
    message:
      "Please complete your account verification to unlock all features. Upload your ID document to continue.",
    type: "warning",
    is_read: false,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
    action_url: "/member/verify-identity",
    action_text: "Verify Now",
  },
  {
    id: 5,
    title: "Delivery Assignment",
    message:
      "You have been assigned a new delivery task in your area. Please check the details and confirm availability.",
    type: "info",
    is_read: true,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
    action_url: "/member/deliveries",
    action_text: "View Delivery",
  },
  {
    id: 6,
    title: "System Maintenance",
    message:
      "Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM EST. Some features may be temporarily unavailable.",
    type: "warning",
    is_read: true,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
  },
  {
    id: 7,
    title: "Welcome to eBaDollar!",
    message:
      "Thank you for joining eBaDollar! Complete your profile to start earning and trading on our platform.",
    type: "info",
    is_read: true,
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 1 week ago
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
    action_url: "/member/profile",
    action_text: "Complete Profile",
  },
];

const MemberNotificationsListPage = () => {
  const { success, error: showError } = useToast();
  const { refreshUnreadCount } = useNotifications();

  // State management
  const [notifications, setNotifications] =
    useState<Notification[]>(MOCK_NOTIFICATIONS);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<"all" | "unread" | "read">(
    "all"
  );
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>(
    []
  );
  const [actionLoading, setActionLoading] = useState(false);

  // Load notifications on component mount and filter change (using mock data for now)
  useEffect(() => {
    // For now, just use mock data - no API call needed
    setNotifications(MOCK_NOTIFICATIONS);
    setLoading(false);
    setError(null);
  }, [activeFilter]);

  const reloadNotifications = useCallback(() => {
    setNotifications(MOCK_NOTIFICATIONS);
    setLoading(false);
    setError(null);
  }, []);

  const markAsRead = useCallback(
    async (notificationIds: number[]) => {
      try {
        setActionLoading(true);

        // For now, just update local state without API call
        // const response = await sdk.request({
        //   endpoint: "/v2/api/ebadollar/custom/member/notifications/mark-read",
        //   method: "POST",
        //   body: { notification_ids: notificationIds },
        // });

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Update local state
        setNotifications((prev) =>
          prev.map((notif) =>
            notificationIds.includes(notif.id)
              ? { ...notif, is_read: true }
              : notif
          )
        );
        setSelectedNotifications([]);
        success("Notifications marked as read");
        // Refresh the global unread count
        refreshUnreadCount();
      } catch (error: any) {
        console.error("Error marking notifications as read:", error);
        showError("Failed to mark notifications as read");
      } finally {
        setActionLoading(false);
      }
    },
    [success, showError, refreshUnreadCount]
  );

  const deleteNotifications = useCallback(
    async (notificationIds: number[]) => {
      try {
        setActionLoading(true);

        // For now, just update local state without API call
        // const response = await sdk.request({
        //   endpoint: "/v2/api/ebadollar/custom/member/notifications/delete",
        //   method: "DELETE",
        //   body: { notification_ids: notificationIds },
        // });

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Update local state
        setNotifications((prev) =>
          prev.filter((notif) => !notificationIds.includes(notif.id))
        );
        setSelectedNotifications([]);
        success("Notifications deleted");
        // Refresh the global unread count
        refreshUnreadCount();
      } catch (error: any) {
        console.error("Error deleting notifications:", error);
        showError("Failed to delete notifications");
      } finally {
        setActionLoading(false);
      }
    },
    [success, showError, refreshUnreadCount]
  );

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} min${diffInMinutes !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case "warning":
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />;
      case "error":
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <InformationCircleIcon className="h-6 w-6 text-blue-500" />;
    }
  };

  const toggleNotificationSelection = (notificationId: number) => {
    setSelectedNotifications((prev) =>
      prev.includes(notificationId)
        ? prev.filter((id) => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const toggleSelectAll = () => {
    const filteredNotifications = getFilteredNotifications();
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map((n) => n.id));
    }
  };

  const getFilteredNotifications = () => {
    switch (activeFilter) {
      case "unread":
        return notifications.filter((n) => !n.is_read);
      case "read":
        return notifications.filter((n) => n.is_read);
      default:
        return notifications;
    }
  };

  const filteredNotifications = getFilteredNotifications();
  const unreadCount = notifications.filter((n) => !n.is_read).length;

  return (
    <MemberWrapper>
      <div className="h-full" style={{ backgroundColor: "#0F2C59" }}>
        <div className="p-6">
          {/* Header */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <BellIcon className="h-8 w-8 text-[#0F2C59] mr-3" />
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Notifications
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Stay updated with your latest activities
                    {unreadCount > 0 && (
                      <span className="ml-2 bg-[#E63946] text-white px-2 py-1 rounded-full text-xs font-medium">
                        {unreadCount} unread
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
              {[
                { key: "all", label: "All", count: notifications.length },
                { key: "unread", label: "Unread", count: unreadCount },
                {
                  key: "read",
                  label: "Read",
                  count: notifications.length - unreadCount,
                },
              ].map((filter) => (
                <button
                  key={filter.key}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeFilter === filter.key
                      ? "bg-[#0F2C59] text-white"
                      : "text-gray-600 hover:bg-gray-200"
                  }`}
                  onClick={() => setActiveFilter(filter.key as any)}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Bulk Actions */}
            {selectedNotifications.length > 0 && (
              <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg mb-4">
                <span className="text-sm text-gray-600">
                  {selectedNotifications.length} notification
                  {selectedNotifications.length !== 1 ? "s" : ""} selected
                </span>
                <div className="flex space-x-2">
                  <InteractiveButton
                    className="px-4 py-2 text-sm bg-[#0F2C59] text-white rounded-md hover:bg-opacity-90"
                    onClick={() => markAsRead(selectedNotifications)}
                    disabled={actionLoading}
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Mark as Read
                  </InteractiveButton>
                  <InteractiveButton
                    className="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                    onClick={() => deleteNotifications(selectedNotifications)}
                    disabled={actionLoading}
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    Delete
                  </InteractiveButton>
                </div>
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="bg-white rounded-lg">
            {loading ? (
              <div className="flex items-center justify-center p-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0F2C59]"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center p-12 text-red-500">
                <div className="text-center">
                  <XCircleIcon className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg mb-2">{error}</p>
                  <InteractiveButton
                    onClick={reloadNotifications}
                    className="px-4 py-2 bg-[#0F2C59] text-white rounded hover:bg-opacity-90"
                  >
                    Retry
                  </InteractiveButton>
                </div>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="flex items-center justify-center p-12 text-gray-500">
                <div className="text-center">
                  <BellIcon className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg mb-2">No notifications</p>
                  <p className="text-sm">
                    {activeFilter === "unread"
                      ? "You're all caught up! No unread notifications."
                      : activeFilter === "read"
                        ? "No read notifications yet."
                        : "You don't have any notifications yet."}
                  </p>
                </div>
              </div>
            ) : (
              <>
                {/* Select All Header */}
                <div className="flex items-center p-4 border-b border-gray-200">
                  <input
                    type="checkbox"
                    checked={
                      selectedNotifications.length ===
                      filteredNotifications.length
                    }
                    onChange={toggleSelectAll}
                    className="h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
                  />
                  <span className="ml-3 text-sm text-gray-600">
                    Select all notifications
                  </span>
                </div>

                {/* Notifications */}
                <div className="divide-y divide-gray-200">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start p-4 hover:bg-gray-50 transition-colors ${
                        !notification.is_read ? "bg-blue-50" : ""
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(
                          notification.id
                        )}
                        onChange={() =>
                          toggleNotificationSelection(notification.id)
                        }
                        className="h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded mt-1"
                      />

                      <div className="ml-3 flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="ml-3 flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3
                              className={`text-sm font-medium ${
                                !notification.is_read
                                  ? "text-gray-900"
                                  : "text-gray-700"
                              }`}
                            >
                              {notification.title}
                            </h3>
                            <p
                              className={`mt-1 text-sm ${
                                !notification.is_read
                                  ? "text-gray-800"
                                  : "text-gray-600"
                              }`}
                            >
                              {notification.message}
                            </p>
                            {notification.action_url &&
                              notification.action_text && (
                                <InteractiveButton
                                  className="mt-2 text-sm text-[#0F2C59] hover:text-[#E63946] font-medium"
                                  onClick={() =>
                                    window.open(
                                      notification.action_url,
                                      "_blank"
                                    )
                                  }
                                >
                                  {notification.action_text} →
                                </InteractiveButton>
                              )}
                          </div>

                          <div className="flex items-center ml-4">
                            {!notification.is_read && (
                              <span className="w-2 h-2 bg-[#E63946] rounded-full mr-3"></span>
                            )}
                            <span className="text-xs text-gray-500 whitespace-nowrap">
                              {formatTime(notification.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberNotificationsListPage;
